import {
  Table as BLMTable,
  ColumnType,
  ColumnsType,
  Dropdown,
  MenuProps,
  PaginationProps,
} from '@blmcp/ui';
import { ResizeCallbackData } from 'react-resizable';
import { DataSourceItemType } from 'antd/es/auto-complete';
import { useImmer } from 'use-immer';
import { useEffect, useMemo } from 'react';
import queryCenterExp from '@/pages/lego/libraryMaterials/module/Query';
import SortDownIcon from '@/assets/lego/sort-down.svg';
import SortUpIcon from '@/assets/lego/sort-up.svg';
import SortNoIcon from '@/assets/lego/sort-no.svg';
import isMobile from '../../utils/isMobile';
import { BaseChart, SortType } from '../../components/types';
import { ResizableTitle } from './ResizableTitle';
import './index.less';
import styles from './index.module.less';

interface TableData {
  columns: ColumnType<unknown>[];
  dataSource: Record<string, unknown>;
  pageNum: number;
  pageSize: number;
  total: number;
}
interface OperationColsItem {
  label: string;
  key: string;
  placeholder: string;
  url: string;
}
interface TableProps extends Partial<BaseChart> {
  dataSource: TableData;
  query: (params: { paging: { pageNum: number; pageSize: number } }) => void;
  operationCols: { freeze: boolean; operationList: OperationColsItem[] };
  chartId: string;
  uuid: string;
}

const render = (template: string, obj: Record<string, any>) => {
  const variableRegex = /\[([^[\]]+)\]/g;
  const getVariableValue = (variable: any) => {
    // [ 'name' ]、[ 'age' ]、[ 'job', 'name' ]
    variable = variable.split('.');
    let variableValue = obj;
    // For example, if we want to get the value of job.name, we will go through the following steps
    // Initialization: variableValue = { name: 'fatfish', age: 100, job: { name: "front end development" } }
    // first loop: variableValue = { name: "front end development" }
    // Second loop: variableValue = 'front end development'
    // Third loop: finished, return 'front end development'
    while (variable.length) {
      variableValue = variableValue[variable.shift()];
    }
    return variableValue;
  };
  const renderStr = template.replace(variableRegex, ($0, variable) => {
    return getVariableValue(variable);
  });
  return renderStr;
};

export const Table = ({
  height = 380,
  width,
  dataSource,
  query,
  operationCols,
  chartId,
  uuid,
}: TableProps) => {
  const queryCenter = queryCenterExp(uuid);
  const { columns, dataSource: dataS, total, pageNum, pageSize } = dataSource;

  const { freeze = true, operationList } = operationCols ?? {};

  const [finalColumns, setColumns] =
    useImmer<ColumnsType<DataSourceItemType>>(columns);

  const operationColumn = useMemo(
    () => ({
      key: 'operation',
      title: '操作',
      dataIndex: 'operation',
      width: 180,
      fixed: !isMobile() && freeze ? 'right' : false, // 移动端不冻结
      render(text, record, index) {
        const mapObj = {};
        columns.forEach((item) => {
          mapObj[item.title] = record[item.key];
        });
        if (operationList.length <= 3) {
          // 全部依次展示
          return (
            <div className="lego-operation-wrapper">
              {operationList?.map((oper) => {
                return (
                  <a
                    key={oper.url}
                    target="_blank"
                    href={oper.url ? render(oper.url, mapObj) : null}
                    rel="noreferrer"
                  >
                    <span className="lego-operation-item">{oper.label}</span>
                  </a>
                );
              })}
            </div>
          );
        } else {
          // 只展示三个，剩余放在更多里边

          const head = operationList.slice(0, 3);
          const tail = operationList.slice(3);

          const items: MenuProps['items'] = tail?.map((oper) => {
            return {
              key: oper.url + oper.label,
              label: (
                <a
                  key={oper.url}
                  target="_blank"
                  href={oper.url ? render(oper.url, mapObj) : null}
                  rel="noreferrer"
                >
                  <span className="lego-operation-item">{oper.label}</span>
                </a>
              ),
            };
          });
          return (
            <div className="lego-operation-wrapper">
              {head?.map((oper) => {
                return (
                  <a
                    key={oper.url}
                    target="_blank"
                    href={oper.url ? render(oper.url, mapObj) : null}
                    rel="noreferrer"
                  >
                    <span className="lego-operation-item">{oper.label}</span>
                  </a>
                );
              })}
              <span className={styles.more}>
                <Dropdown menu={{ items }}>
                  <a onClick={(e) => e.preventDefault()}>
                    <span>更多</span>
                  </a>
                </Dropdown>
              </span>
            </div>
          );
        }
      },
    }),
    [freeze, columns, operationList],
  );

  useEffect(() => {
    const finalOperationColumn =
      operationList?.length > 0 ? [operationColumn] : [];
    setColumns([...columns, ...finalOperationColumn]);
  }, [columns, operationColumn, setColumns]);

  // const finalColumns = columns;
  // const onChange = (pageNum: number, pageSize: number) => {
  //   const paging = {
  //     // 分页配置
  //     pageSize, // 分页阈值
  //     pageNum, // 当前页数
  //   };
  //   const filterInfo = queryCenter.getQuery();
  //   query({ ...filterInfo, paging });
  // };
  const tableChange = (
    { pageSize, current }: { pageSize: number; current: number },
    filters,
    sorter,
  ) => {
    const sort = [];

    if (sorter.order) {
      sort.push({
        // 排序
        index: sorter.column.index + 1, // 排序列
        sortOrder: sorter.order === 'ascend' ? SortType.ASC : SortType.DESC, // 排序方式，0:asc 升序  1:desc 降序
        key: sorter.field,
      });
    }
    const filterInfo = queryCenter.getQuery();
    const paging = {
      pageSize, // 分页阈值
      pageNum: current, // 当前页数
    };
    if (isMobile()) {
      if (!paging.pageSize) {
        paging.pageSize = 20;
      }
      if (!paging.pageNum) {
        paging.pageNum = 1;
      }
    }
    queryCenter.setTableQuery(chartId, { paging, sort });
    query({ ...filterInfo, paging, sort });
  };

  const iconStyle = {
    display: 'flex',
    alignItems: 'center',
    height: '100%',
    marginLeft: '2px',
  };

  const getSortIcon = ({ sortOrder }: { sortOrder: string | null }) => {
    if (sortOrder === 'ascend') {
      return (
        <div style={iconStyle}>
          <SortUpIcon />
        </div>
      );
    } else if (sortOrder === 'descend') {
      return (
        <div style={iconStyle}>
          <SortDownIcon />
        </div>
      );
    } else {
      return (
        <div style={iconStyle}>
          <SortNoIcon />
        </div>
      );
    }
  };

  // 动态修改宽度
  const handleResize =
    (index: number) =>
    (_: React.SyntheticEvent<Element>, { size }: ResizeCallbackData) => {
      setColumns((prevColumns) => {
        prevColumns[index].width = size.width;
      });
    };
  // 设置侦听函数
  const mergeColumns: ColumnsType<DataSourceItemType> = finalColumns.map(
    (col, index) => ({
      ...col,
      onHeaderCell: (column) => ({
        width: (column as ColumnType<DataSourceItemType>).width,
        onResize: handleResize(index),
      }),
      sorter: col.dataIndex === 'operation' ? false : true,
      sortIcon: getSortIcon,
    }),
  );
  const showTotal: PaginationProps['showTotal'] = (total) => `共 ${total} 条`;
  let pagination = {
    simple: isMobile() ? { readOnly: true } : undefined,
    total,
    pageSize,
    current: pageNum,
    // onChange,
    pageSizeOptions: [20, 50, 100],
    showTotal: isMobile() ? () => false : showTotal,
    showSizeChanger: !isMobile(),
  };
  if (isMobile() && total <= 20) {
    // 如果在移动端，仅有一页，则不显示分页
    pagination = false;
  }
  return (
    <div
      className={`lego-table-wrap lego-bi-scroll-hide ${
        isMobile() ? 'mobile' : ''
      }`}
      style={{ maxWidth: width }}
    >
      <BLMTable
        columns={mergeColumns}
        dataSource={dataS}
        onChange={tableChange}
        scroll={{ y: isMobile() ? 240 : height - 75, x: 'max-content' }}
        tableLayout="auto"
        showSorterTooltip={false}
        // 拖拽头部组件
        components={{
          header: {
            cell: ResizableTitle,
          },
        }}
        pagination={pagination}
      />
    </div>
  );
};
