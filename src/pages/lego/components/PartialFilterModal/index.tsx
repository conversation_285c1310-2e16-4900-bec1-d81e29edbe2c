import React, { useState, useEffect } from 'react';
import { Modal, Button, message, Tooltip } from '@blmcp/ui';
import {
  PlusOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import './index.less';

// 生成唯一ID
const generateId = () => Math.random().toString(36).substr(2, 9);

// 模拟数据
const mockChartData = [
  {
    datasetId: 'dataset1',
    datasetName: '数据集1',
    charts: [
      { id: 'chart1', name: '明细表' },
      { id: 'chart2', name: '明细表COPIED...' },
    ],
  },
  {
    datasetId: 'dataset2',
    datasetName: '数据集2',
    charts: [
      { id: 'chart3', name: '明细表COPIED' },
      { id: 'chart4', name: '柱形图计单' },
      { id: 'chart5', name: '订单统计' },
    ],
  },
];

const mockFieldOptions = [
  { label: 'str 司机名称', value: 'driver_name' },
  { label: 'str 城市', value: 'city' },
  { label: 'str 运力公司', value: 'transport_company' },
  { label: 'str 省份', value: 'province' },
  { label: 'str 省份编码', value: 'province_code' },
  { label: 'str 状态', value: 'status' },
];

const MAX_CONDITIONS = 5;

const PartialFilterModal = ({
  isModalOpen,
  handleCloseModal,
  initialData = [],
  onSave,
}) => {
  const [filterConditions, setFilterConditions] = useState([]);
  const [activeConditionId, setActiveConditionId] = useState(null);

  useEffect(() => {
    if (isModalOpen) {
      if (initialData.length > 0) {
        setFilterConditions(initialData);
        setActiveConditionId(initialData[0].id);
      } else {
        setFilterConditions([]);
        setActiveConditionId(null);
      }
    }
  }, [isModalOpen, initialData]);

  // 验证函数
  const validateFilters = (conditions) => {
    if (conditions.length === 0) {
      return { isValid: true, message: '' };
    }

    // 检查名称重复
    const names = conditions.map((c) => c.name);
    const duplicateNames = names.filter(
      (name, index) => names.indexOf(name) !== index,
    );
    if (duplicateNames.length > 0) {
      return {
        isValid: false,
        message: '筛选器名称重复',
      };
    }

    // 检查配置完整性
    for (const condition of conditions) {
      if (condition.selectedCharts.length === 0) {
        return {
          isValid: false,
          message: '请先选择关联图表及字段',
        };
      }

      for (const chartId of condition.selectedCharts) {
        if (!condition.selectedFields[chartId]) {
          return {
            isValid: false,
            message: '请先选择关联图表及字段',
          };
        }
      }
    }

    return { isValid: true, message: '' };
  };

  // 保存筛选条件
  const handleSave = () => {
    const validation = validateFilters(filterConditions);
    if (!validation.isValid) {
      message.error(validation.message);
      return;
    }

    onSave?.(filterConditions);
    handleCloseModal();
  };

  const handleCancel = () => {
    handleCloseModal();
  };

  // 添加查询条件
  const addCondition = () => {
    if (filterConditions.length >= MAX_CONDITIONS) return;

    const newCondition = {
      id: generateId(),
      name: `查询条件${filterConditions.length + 1}`,
      type: 'time',
      selectedCharts: [],
      selectedFields: {},
    };

    const newConditions = [...filterConditions, newCondition];
    setFilterConditions(newConditions);
    setActiveConditionId(newCondition.id);
  };

  // 删除查询条件
  const deleteCondition = (id) => {
    const newConditions = filterConditions.filter(
      (condition) => condition.id !== id,
    );
    setFilterConditions(newConditions);

    if (activeConditionId === id) {
      setActiveConditionId(
        newConditions.length > 0 ? newConditions[0].id : null,
      );
    }
  };

  // 更新条件名称
  const updateConditionName = (id, name) => {
    const validName = name
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9（）()]/g, '')
      .slice(0, 48);

    const newConditions = filterConditions.map((condition) =>
      condition.id === id ? { ...condition, name: validName } : condition,
    );
    setFilterConditions(newConditions);
  };

  // 选择条件
  const selectCondition = (id) => {
    setActiveConditionId(id);
  };

  // 检查错误
  const hasFilterError = (condition) => {
    const hasNoCharts = condition.selectedCharts.length === 0;
    const hasUnselectedFields = condition.selectedCharts.some(
      (chartId) => !condition.selectedFields[chartId],
    );
    const hasDuplicateName =
      filterConditions.filter(
        (c) => c.name === condition.name && c.id !== condition.id,
      ).length > 0;

    return hasNoCharts || hasUnselectedFields || hasDuplicateName;
  };

  // 获取错误信息
  const getFilterErrorMessage = (condition) => {
    const hasNoCharts = condition.selectedCharts.length === 0;
    const hasUnselectedFields = condition.selectedCharts.some(
      (chartId) => !condition.selectedFields[chartId],
    );
    const hasDuplicateName =
      filterConditions.filter(
        (c) => c.name === condition.name && c.id !== condition.id,
      ).length > 0;

    if (hasDuplicateName) return '筛选器名称重复';
    if (hasNoCharts || hasUnselectedFields) return '请先选择关联图表及字段';
    return '';
  };

  return (
    <Modal
      title="查询条件设置"
      open={isModalOpen}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="confirm" type="primary" onClick={handleSave}>
          确认
        </Button>,
      ]}
      onOK
      width={1000}
      className="partial-filter-modal"
    >
      <div className="partial-filter-content">
        <div className="filter-sidebar">
          <div className="sidebar-header">
            <span className="sidebar-title">查询条件</span>
            <Tooltip
              title={
                filterConditions.length >= MAX_CONDITIONS
                  ? '局部筛选器最多支持5个'
                  : ''
              }
            >
              <Button
                type="text"
                icon={<PlusOutlined />}
                onClick={addCondition}
                disabled={filterConditions.length >= MAX_CONDITIONS}
                className="add-button"
              />
            </Tooltip>
          </div>

          <div className="condition-list">
            {filterConditions.map((condition) => (
              <div
                key={condition.id}
                className={`condition-item ${
                  activeConditionId === condition.id ? 'active' : ''
                }`}
                onClick={() => selectCondition(condition.id)}
              >
                <Tooltip title={condition.name} placement="topLeft">
                  <Input
                    value={condition.name}
                    onChange={(e) =>
                      updateConditionName(condition.id, e.target.value)
                    }
                    onClick={(e) => e.stopPropagation()}
                    className="condition-input"
                    bordered={false}
                    maxLength={48}
                  />
                </Tooltip>

                {hasFilterError(condition) && (
                  <Tooltip title={getFilterErrorMessage(condition)}>
                    <ExclamationCircleOutlined className="error-icon" />
                  </Tooltip>
                )}

                <Button
                  type="text"
                  icon={<DeleteOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    deleteCondition(condition.id);
                  }}
                  className="delete-button"
                  size="small"
                />
              </div>
            ))}
          </div>
        </div>

        <div className="filter-main">
          {!activeConditionId ? (
            <div className="empty-state">
              <div>请先新建查询条件</div>
            </div>
          ) : (
            <FilterConfigContent
              condition={filterConditions.find(
                (c) => c.id === activeConditionId,
              )}
              onConditionUpdate={(updates) => {
                const newConditions = filterConditions.map((condition) =>
                  condition.id === activeConditionId
                    ? { ...condition, ...updates }
                    : condition,
                );
                setFilterConditions(newConditions);
              }}
            />
          )}
        </div>
      </div>
    </Modal>
  );
};

// 配置面板内容组件
const FilterConfigContent = ({ condition, onConditionUpdate }) => {
  const [disabledGroups, setDisabledGroups] = useState([]);

  useEffect(() => {
    if (condition.type === 'list' && condition.selectedCharts.length > 0) {
      const selectedDatasets = new Set();
      mockChartData.forEach((group) => {
        if (
          group.charts.some((chart) =>
            condition.selectedCharts.includes(chart.id),
          )
        ) {
          selectedDatasets.add(group.datasetId);
        }
      });

      const disabled = mockChartData
        .filter((group) => !selectedDatasets.has(group.datasetId))
        .map((group) => group.datasetId);

      setDisabledGroups(disabled);
    } else {
      setDisabledGroups([]);
    }
  }, [condition.type, condition.selectedCharts]);

  const handleTypeChange = (e) => {
    const newType = e.target.value;
    onConditionUpdate({
      type: newType,
      selectedCharts: [],
      selectedFields: {},
    });
  };

  const handleChartSelect = (chartId, checked) => {
    let newSelectedCharts;
    let newSelectedFields = { ...condition.selectedFields };

    if (checked) {
      newSelectedCharts = [...condition.selectedCharts, chartId];
    } else {
      newSelectedCharts = condition.selectedCharts.filter(
        (id) => id !== chartId,
      );
      delete newSelectedFields[chartId];
    }

    onConditionUpdate({
      selectedCharts: newSelectedCharts,
      selectedFields: newSelectedFields,
    });
  };

  const handleGroupSelectAll = (groupId, selectAll) => {
    const group = mockChartData.find((g) => g.datasetId === groupId);
    if (!group) return;

    let newSelectedCharts = [...condition.selectedCharts];
    let newSelectedFields = { ...condition.selectedFields };

    if (selectAll) {
      group.charts.forEach((chart) => {
        if (!newSelectedCharts.includes(chart.id)) {
          newSelectedCharts.push(chart.id);
        }
      });
    } else {
      group.charts.forEach((chart) => {
        newSelectedCharts = newSelectedCharts.filter((id) => id !== chart.id);
        delete newSelectedFields[chart.id];
      });
    }

    onConditionUpdate({
      selectedCharts: newSelectedCharts,
      selectedFields: newSelectedFields,
    });
  };

  const handleFieldSelect = (chartId, fieldValue) => {
    onConditionUpdate({
      selectedFields: {
        ...condition.selectedFields,
        [chartId]: fieldValue,
      },
    });
  };

  const isGroupSelected = (groupId) => {
    const group = mockChartData.find((g) => g.datasetId === groupId);
    if (!group) return false;
    return group.charts.every((chart) =>
      condition.selectedCharts.includes(chart.id),
    );
  };

  const isGroupPartiallySelected = (groupId) => {
    const group = mockChartData.find((g) => g.datasetId === groupId);
    if (!group) return false;
    const selectedCount = group.charts.filter((chart) =>
      condition.selectedCharts.includes(chart.id),
    ).length;
    return selectedCount > 0 && selectedCount < group.charts.length;
  };

  return (
    <div className="config-panel">
      <div className="filter-type-section">
        <div className="section-title">筛选器类型</div>
        <Radio.Group value={condition.type} onChange={handleTypeChange}>
          <Radio value="time">时间筛选器</Radio>
          <Radio value="range">区间筛选器</Radio>
          <Radio value="text">文本筛选器</Radio>
          <Radio value="list">列表筛选器</Radio>
        </Radio.Group>

        {condition.type === 'list' && (
          <div className="list-filter-notice">
            列表筛选器仅支持配置同数据图表。
          </div>
        )}
      </div>

      <div className="chart-selection-section">
        <div className="chart-groups">
          {mockChartData.map((group) => {
            const isDisabled = disabledGroups.includes(group.datasetId);
            const isSelected = isGroupSelected(group.datasetId);
            const isPartiallySelected = isGroupPartiallySelected(
              group.datasetId,
            );

            return (
              <div key={group.datasetId} className="chart-group">
                <div className="group-header">
                  <div className="group-title">{group.datasetName}</div>
                  <div className="group-actions">
                    <Button
                      size="small"
                      onClick={() =>
                        handleGroupSelectAll(group.datasetId, true)
                      }
                      disabled={isDisabled || isSelected}
                      className="action-button"
                    >
                      全选
                    </Button>
                    <Button
                      size="small"
                      onClick={() =>
                        handleGroupSelectAll(group.datasetId, false)
                      }
                      disabled={
                        isDisabled || (!isSelected && !isPartiallySelected)
                      }
                      className="action-button"
                    >
                      取消选择
                    </Button>
                  </div>
                </div>

                <div
                  className={`group-content ${isDisabled ? 'disabled' : ''}`}
                >
                  {group.charts.map((chart) => {
                    const isChartSelected = condition.selectedCharts.includes(
                      chart.id,
                    );
                    const selectedField = condition.selectedFields[chart.id];

                    return (
                      <div key={chart.id} className="chart-item">
                        <div className="chart-info">
                          <Tooltip
                            title={
                              isDisabled
                                ? '自定义筛选仅支持单个数据集图表配置'
                                : ''
                            }
                          >
                            <Checkbox
                              checked={isChartSelected}
                              onChange={(e) =>
                                handleChartSelect(chart.id, e.target.checked)
                              }
                              disabled={isDisabled}
                              className="chart-checkbox"
                            />
                          </Tooltip>
                          <span className="chart-name">{chart.name}</span>
                        </div>

                        {isChartSelected && (
                          <Select
                            value={selectedField}
                            onChange={(value) =>
                              handleFieldSelect(chart.id, value)
                            }
                            placeholder="请选择字段"
                            className="field-select"
                            options={mockFieldOptions}
                          />
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default PartialFilterModal;
