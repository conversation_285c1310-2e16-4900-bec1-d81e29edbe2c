interface FilterCondition {
  id: string;
  name: string;
  type: 'time' | 'range' | 'text' | 'list';
  selectedCharts: string[];
  selectedFields: { [chartId: string]: string };
}

interface ValidationResult {
  isValid: boolean;
  message: string;
}

/**
 * 验证筛选器配置
 */
export const validateFilters = (conditions: FilterCondition[]): ValidationResult => {
  if (conditions.length === 0) {
    return { isValid: true, message: '' };
  }

  // 检查名称重复
  const names = conditions.map(c => c.name);
  const duplicateNames = names.filter((name, index) => names.indexOf(name) !== index);
  if (duplicateNames.length > 0) {
    return { 
      isValid: false, 
      message: '筛选器名称重复' 
    };
  }

  // 检查配置完整性
  for (const condition of conditions) {
    // 检查是否选择了图表
    if (condition.selectedCharts.length === 0) {
      return { 
        isValid: false, 
        message: '请先选择关联图表及字段' 
      };
    }

    // 检查是否为每个选中的图表都选择了字段
    for (const chartId of condition.selectedCharts) {
      if (!condition.selectedFields[chartId]) {
        return { 
          isValid: false, 
          message: '请先选择关联图表及字段' 
        };
      }
    }
  }

  return { isValid: true, message: '' };
};

/**
 * 检查单个筛选器是否有错误
 */
export const hasFilterError = (
  condition: FilterCondition, 
  allConditions: FilterCondition[]
): boolean => {
  // 检查是否有配置错误
  const hasNoCharts = condition.selectedCharts.length === 0;
  const hasUnselectedFields = condition.selectedCharts.some(
    chartId => !condition.selectedFields[chartId]
  );
  const hasDuplicateName = allConditions.filter(
    c => c.name === condition.name && c.id !== condition.id
  ).length > 0;
  
  return hasNoCharts || hasUnselectedFields || hasDuplicateName;
};

/**
 * 获取筛选器错误提示信息
 */
export const getFilterErrorMessage = (
  condition: FilterCondition, 
  allConditions: FilterCondition[]
): string => {
  const hasNoCharts = condition.selectedCharts.length === 0;
  const hasUnselectedFields = condition.selectedCharts.some(
    chartId => !condition.selectedFields[chartId]
  );
  const hasDuplicateName = allConditions.filter(
    c => c.name === condition.name && c.id !== condition.id
  ).length > 0;
  
  if (hasDuplicateName) return '筛选器名称重复';
  if (hasNoCharts || hasUnselectedFields) return '请先选择关联图表及字段';
  return '';
};
