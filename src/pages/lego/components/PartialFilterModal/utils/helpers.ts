/**
 * 生成唯一ID
 */
export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

/**
 * 验证字符串是否符合要求（中文、英文字母、中英文括号、数字）
 */
export const validateCharacters = (str: string): boolean => {
  const regex = /^[\u4e00-\u9fa5a-zA-Z0-9（）()]*$/;
  return regex.test(str);
};

/**
 * 清理字符串，只保留允许的字符
 */
export const cleanString = (str: string): string => {
  return str.replace(/[^\u4e00-\u9fa5a-zA-Z0-9（）()]/g, '');
};

/**
 * 限制字符串长度
 */
export const limitLength = (str: string, maxLength: number): string => {
  return str.slice(0, maxLength);
};
