/**
 * 模拟图表数据
 */
export const mockChartData = [
  {
    datasetId: 'dataset1',
    datasetName: '数据集1',
    charts: [
      { id: 'chart1', name: '明细表' },
      { id: 'chart2', name: '明细表COPIED...' },
    ],
  },
  {
    datasetId: 'dataset2', 
    datasetName: '数据集2',
    charts: [
      { id: 'chart3', name: '明细表COPIED' },
      { id: 'chart4', name: '柱形图计单' },
      { id: 'chart5', name: '订单统计' },
    ],
  },
];

/**
 * 模拟字段选项数据
 */
export const mockFieldOptions = [
  { label: 'str 司机名称', value: 'driver_name' },
  { label: 'str 城市', value: 'city' },
  { label: 'str 运力公司', value: 'transport_company' },
  { label: 'str 省份', value: 'province' },
  { label: 'str 省份编码', value: 'province_code' },
  { label: 'str 状态', value: 'status' },
];

/**
 * 模拟初始数据（用于编辑回填）
 */
export const mockInitialData = [
  {
    id: 'condition1',
    name: '查询条件1',
    type: 'time' as const,
    selectedCharts: ['chart1', 'chart2'],
    selectedFields: {
      chart1: 'driver_name',
      chart2: 'city',
    },
  },
  {
    id: 'condition2', 
    name: '查询条件2',
    type: 'list' as const,
    selectedCharts: ['chart3'],
    selectedFields: {
      chart3: 'status',
    },
  },
];
