import { Button, Input, Tooltip } from '@blmcp/ui';
import {
  PlusOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { generateId } from '../utils/helpers';
import { hasFilterError, getFilterErrorMessage } from '../utils/validation';

interface FilterCondition {
  id: string;
  name: string;
  type: 'time' | 'range' | 'text' | 'list';
  selectedCharts: string[];
  selectedFields: { [chartId: string]: string };
}

interface FilterConditionListProps {
  conditions: FilterCondition[];
  activeConditionId: string | null;
  onConditionsChange: (conditions: FilterCondition[]) => void;
  onActiveConditionChange: (id: string | null) => void;
}

const MAX_CONDITIONS = 5;

const FilterConditionList = ({
  conditions,
  activeConditionId,
  onConditionsChange,
  onActiveConditionChange,
}: FilterConditionListProps) => {
  const addCondition = () => {
    if (conditions.length >= MAX_CONDITIONS) return;

    const newCondition: FilterCondition = {
      id: generateId(),
      name: `查询条件${conditions.length + 1}`,
      type: 'time',
      selectedCharts: [],
      selectedFields: {},
    };

    const newConditions = [...conditions, newCondition];
    onConditionsChange(newConditions);
    onActiveConditionChange(newCondition.id);
  };

  const deleteCondition = (id: string) => {
    const newConditions = conditions.filter((condition) => condition.id !== id);
    onConditionsChange(newConditions);

    if (activeConditionId === id) {
      onActiveConditionChange(
        newConditions.length > 0 ? newConditions[0].id : null,
      );
    }
  };

  const updateConditionName = (id: string, name: string) => {
    // 限制字符数和字符类型
    const validName = name
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9（）()]/g, '')
      .slice(0, 48);

    const newConditions = conditions.map((condition) =>
      condition.id === id ? { ...condition, name: validName } : condition,
    );
    onConditionsChange(newConditions);
  };

  const selectCondition = (id: string) => {
    onActiveConditionChange(id);
  };

  return (
    <div>
      <div className="sidebar-header">
        <span className="sidebar-title">查询条件</span>
        <Tooltip
          title={
            conditions.length >= MAX_CONDITIONS ? '局部筛选器最多支持5个' : ''
          }
        >
          <Button
            type="text"
            icon={<PlusOutlined />}
            onClick={addCondition}
            disabled={conditions.length >= MAX_CONDITIONS}
            className="add-button"
          />
        </Tooltip>
      </div>

      <div className="condition-list">
        {conditions.map((condition) => (
          <div
            key={condition.id}
            className={`condition-item ${
              activeConditionId === condition.id ? 'active' : ''
            }`}
            onClick={() => selectCondition(condition.id)}
          >
            <Tooltip title={condition.name} placement="topLeft">
              <Input
                value={condition.name}
                onChange={(e) =>
                  updateConditionName(condition.id, e.target.value)
                }
                onClick={(e) => e.stopPropagation()}
                className="condition-input"
                bordered={false}
                maxLength={48}
              />
            </Tooltip>

            {hasFilterError(condition, conditions) && (
              <Tooltip title={getFilterErrorMessage(condition, conditions)}>
                <ExclamationCircleOutlined className="error-icon" />
              </Tooltip>
            )}

            <Button
              type="text"
              icon={<DeleteOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                deleteCondition(condition.id);
              }}
              className="delete-button"
              size="small"
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default FilterConditionList;
