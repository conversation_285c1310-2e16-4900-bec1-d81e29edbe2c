import { Radio } from '@blmcp/ui';
import ChartSelectionSection from './ChartSelectionSection';

interface FilterCondition {
  id: string;
  name: string;
  type: 'time' | 'range' | 'text' | 'list';
  selectedCharts: string[];
  selectedFields: { [chartId: string]: string };
}

interface FilterConfigPanelProps {
  conditions: FilterCondition[];
  activeConditionId: string | null;
  onConditionsChange: (conditions: FilterCondition[]) => void;
}

const FilterConfigPanel = ({
  conditions,
  activeConditionId,
  onConditionsChange,
}: FilterConfigPanelProps) => {
  const activeCondition = conditions.find((c) => c.id === activeConditionId);

  const updateActiveCondition = (updates: Partial<FilterCondition>) => {
    if (!activeCondition) return;

    const newConditions = conditions.map((condition) =>
      condition.id === activeConditionId
        ? { ...condition, ...updates }
        : condition,
    );
    onConditionsChange(newConditions);
  };

  const handleTypeChange = (e: any) => {
    const newType = e.target.value;
    updateActiveCondition({
      type: newType,
      selectedCharts: [],
      selectedFields: {},
    });
  };

  if (!activeCondition) {
    return (
      <div className="empty-state">
        <div>请先新建查询条件</div>
      </div>
    );
  }

  return (
    <div className="config-panel">
      <div className="filter-type-section">
        <div className="section-title">筛选器类型</div>
        <Radio.Group value={activeCondition.type} onChange={handleTypeChange}>
          <Radio value="time">时间筛选器</Radio>
          <Radio value="range">区间筛选器</Radio>
          <Radio value="text">文本筛选器</Radio>
          <Radio value="list">列表筛选器</Radio>
        </Radio.Group>

        {activeCondition.type === 'list' && (
          <div className="list-filter-notice">
            列表筛选器仅支持配置同数据图表。
          </div>
        )}
      </div>

      <div className="chart-selection-section">
        <ChartSelectionSection
          condition={activeCondition}
          onConditionUpdate={updateActiveCondition}
        />
      </div>
    </div>
  );
};

export default FilterConfigPanel;
