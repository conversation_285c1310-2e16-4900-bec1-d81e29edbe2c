/*
 * Created on Fri Jan 12 2024
 *
 * Copyright (c) 2024 bai long ma
 */
import dayjs from 'dayjs'; // 全局使用
import quarterOfYear from 'dayjs/plugin/quarterOfYear';

// dayjs.extend(quarterOfYear);
quarterOfYear(undefined, dayjs, dayjs);
dayjs.locale('zh-cn');

// 时间粒度枚举值
export const timeType = {
  DAY: 1,
  WEEK: 2,
  MONTH: 3,
  QUARTER: 4,
  YEAR: 5,
  CUSTOM: 6,
};

// 获取预设时间范围
export const getPresets = (onClick = () => {}) => {
  return [
    {
      label: <div onClick={() => onClick(timeType.DAY)}>今日</div>,
      title: '今日',
      type: 'cur-day',
      timeType: timeType.DAY,
      value: [dayjs().toDate(), dayjs().toDate()],
    },
    {
      label: <div onClick={() => onClick(timeType.DAY)}>昨日</div>,
      title: '昨日',
      type: 'yesterday',
      timeType: timeType.DAY,
      value: [dayjs().add(-1, 'd').toDate(), dayjs().add(-1, 'd').toDate()],
    },
    {
      label: <div onClick={() => onClick(timeType.WEEK)}>本周</div>,
      title: '本周',
      type: 'cur-week',
      timeType: timeType.WEEK,
      value: [dayjs().startOf('week').toDate(), dayjs().toDate()],
    },
    {
      label: <div onClick={() => onClick(timeType.WEEK)}>上周</div>,
      title: '上周',
      timeType: timeType.WEEK,
      type: 'last-week',
      // t-1所在的周的前一周是上周
      value: [
        dayjs().add(-1, 'week').startOf('week').toDate(),
        dayjs().add(-1, 'week').endOf('week').toDate(),
      ],
    },
    {
      label: <div onClick={() => onClick(timeType.MONTH)}>本月</div>,
      title: '本月',
      timeType: timeType.MONTH,
      type: 'cur-month',
      // t-1所在的月是本月
      value: [dayjs().startOf('month').toDate(), dayjs().toDate()],
    },
    {
      label: <div onClick={() => onClick(timeType.MONTH)}>上月</div>,
      title: '上月',
      timeType: timeType.MONTH,
      type: 'last-month',
      // t-1所在的月的前一月是上月
      value: [
        dayjs().add(-1, 'month').startOf('month').toDate(),
        dayjs().add(-1, 'month').endOf('month').toDate(),
      ],
    },
    {
      label: <div onClick={() => onClick(timeType.QUARTER)}>本季</div>,
      title: '本季',
      timeType: timeType.QUARTER,
      type: 'cur-quarter',
      // t-1所在的季是本季
      value: [dayjs().startOf('quarter').toDate(), dayjs().toDate()],
    },
    {
      label: <div onClick={() => onClick(timeType.QUARTER)}>上季</div>,
      title: '上季',
      timeType: timeType.QUARTER,
      type: 'last-quarter',
      // t-1所在的季的前一季是上季
      value: [
        dayjs().add(-1, 'quarter').startOf('quarter').toDate(),
        dayjs().add(-1, 'quarter').endOf('quarter').toDate(),
      ],
    },
    {
      // 选择的是多日，则按照CUSTOM传参
      label: <div onClick={() => onClick(timeType.CUSTOM)}>近7天</div>,
      title: '近7天',
      timeType: timeType.CUSTOM,
      type: 'last-7',
      value: [dayjs().add(-6, 'd').toDate(), dayjs().toDate()],
    },
    {
      // 选择的是多日，则按照CUSTOM传参
      label: <div onClick={() => onClick(timeType.CUSTOM)}>近15天</div>,
      title: '近15天',
      timeType: timeType.CUSTOM,
      type: 'last-15',
      value: [dayjs().add(-14, 'd').toDate(), dayjs().toDate()],
    },
  ];
};

export const getOtherPresets = (onClick = () => {}) => {
  return [
    {
      label: <div onClick={() => onClick(timeType.DAY)}>今日</div>,
      title: '今日',
      type: 'cur-day',
      timeType: timeType.DAY,
      value: [dayjs().toDate(), dayjs().toDate()],
    },
    {
      label: <div onClick={() => onClick(timeType.DAY)}>昨日</div>,
      title: '昨日',
      type: 'yesterday',
      timeType: timeType.DAY,
      value: [dayjs().add(-1, 'd').toDate(), dayjs().add(-1, 'd').toDate()],
    },
    {
      label: <div onClick={() => onClick(timeType.WEEK)}>本周</div>,
      title: '本周',
      type: 'cur-week',
      timeType: timeType.WEEK,
      // t-1所在的周是本周
      value: [dayjs().startOf('week').toDate(), dayjs().toDate()],
    },
    {
      label: <div onClick={() => onClick(timeType.WEEK)}>上周</div>,
      title: '上周',
      timeType: timeType.WEEK,
      type: 'last-week',
      // t-1所在的周的前一周是上周
      value: [
        dayjs().add(-1, 'week').startOf('week').toDate(),
        dayjs().add(-1, 'week').endOf('week').toDate(),
      ],
    },
    {
      label: <div onClick={() => onClick(timeType.MONTH)}>本月</div>,
      title: '本月',
      timeType: timeType.MONTH,
      type: 'cur-month',
      // t-1所在的月是本月
      value: [dayjs().startOf('month').toDate(), dayjs().toDate()],
    },
    {
      label: <div onClick={() => onClick(timeType.MONTH)}>上月</div>,
      title: '上月',
      timeType: timeType.MONTH,
      type: 'last-month',
      // t-1所在的月的前一月是上月
      value: [
        dayjs().add(-1, 'month').startOf('month').toDate(),
        dayjs().add(-1, 'month').endOf('month').toDate(),
      ],
    },
    {
      label: <div onClick={() => onClick(timeType.QUARTER)}>本季</div>,
      title: '本季',
      timeType: timeType.QUARTER,
      type: 'cur-quarter',
      // t-1所在的季是本季
      value: [dayjs().startOf('quarter').toDate(), dayjs().toDate()],
    },
    {
      label: <div onClick={() => onClick(timeType.QUARTER)}>上季</div>,
      title: '上季',
      timeType: timeType.QUARTER,
      type: 'last-quarter',
      // t-1所在的季的前一季是上季
      value: [
        dayjs().add(-1, 'quarter').startOf('quarter').toDate(),
        dayjs().add(-1, 'quarter').endOf('quarter').toDate(),
      ],
    },
    {
      // 选择的是多日，则按照CUSTOM传参
      label: <div onClick={() => onClick(timeType.CUSTOM)}>近7天</div>,
      title: '近7天',
      timeType: timeType.CUSTOM,
      type: 'last-7',
      value: [dayjs().add(-6, 'd').toDate(), dayjs().toDate()],
    },
    {
      // 选择的是多日，则按照CUSTOM传参
      label: <div onClick={() => onClick(timeType.CUSTOM)}>近15天</div>,
      title: '近15天',
      timeType: timeType.CUSTOM,
      type: 'last-15',
      value: [dayjs().add(-14, 'd').toDate(), dayjs().toDate()],
    },
    {
      // 选择的是多日，则按照CUSTOM传参
      label: <div onClick={() => onClick(timeType.CUSTOM, 'clear')}>清空</div>,
      title: '清空',
      timeType: timeType.CUSTOM,
      type: 'clear-time',
      value: [],
    },
  ];
};

// 点击面板，自定义时间范围，获取到时间粒度。如果是整月/周/季，则传月/周/季粒度；当前月/周/季不是整还是按照custom传（产品经理定）
export const getPanelDateFilterRelativeUnit = (date) => {
  // 判断是否是整周
  const isOneWeek = () => {
    if (
      date[0].startOf('week').isSame(date[0], 'day') &&
      date[0].endOf('week').isSame(date[1], 'day')
    ) {
      return true;
    }
  };
  // 判断是否是整月
  const isOneMonth = () => {
    if (
      date[0].startOf('month').isSame(date[0], 'day') &&
      date[0].endOf('month').isSame(date[1], 'day')
    ) {
      return true;
    }
  };
  // 判断是否是整季度
  const isOneQuarter = () => {
    if (
      date[0].startOf('quarter').isSame(date[0], 'day') &&
      date[0].endOf('quarter').isSame(date[1], 'day')
    ) {
      return true;
    }
  };
  if (date[0].isSame(date[1], 'day')) {
    return timeType.DAY;
  } else if (isOneWeek()) {
    return timeType.WEEK;
  } else if (isOneMonth()) {
    return timeType.MONTH;
  } else if (isOneQuarter()) {
    return timeType.QUARTER;
  }
  return timeType.CUSTOM;
};
