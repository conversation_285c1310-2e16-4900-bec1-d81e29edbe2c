// const engine = window.AliLowCodeEngine || {}
// const { project, material, config } = engine || {};
const project = () => window.AliLowCodeEngine?.project;
const material = () => window.AliLowCodeEngine?.material;
const config = () => window.AliLowCodeEngine?.config;
import { IPublicTypeRootSchema } from '@alilc/lowcode-types';
// import { filterPackages } from '@alilc/lowcode-plugin-inject';
import getAssets from '@/pages/lego/modules/editPage/services/getAssets';
import { isHubble } from '@/utils/hubble';
import { DatasetItem, DataSetConfig } from '../type';
import { store, ComponentValue } from '../hooks/useComponent';
import { curSchemaVersion } from '../config/config';

declare global {
  interface Window {
    legoMinAuthLevel: number;
    __setSelectComponentPropsDataById: (
      id: string,
      key: string,
      value: any,
    ) => void;
    __lego_getNodeById: (id: string) => any;
    __lego_insertNode: (...arg: any[]) => any;
    __lego_insertNodes: (...arg: any[]) => any;
  }
}

const TableSheetHeadSortKey = 'firstHeadSortField';

export const LegoReportIdKey = 'LegoReportId';
// 获取所有组件描述信息
export const getComponentMeta = function () {
  if (!material()) return null;
  return (material()?.getAssets()?.components || []).filter(
    (v) => v.componentBehavior,
  );
};

// 根据组件名称获取描述信息
export const getComponentMetaByName = function (componentName: string) {
  return getComponentMeta()?.find(
    (v: any) => v.componentName === componentName,
  );
};

// 获取当前选中的组件
export const getSelectComponentNode = function () {
  return project().currentDocument?.selection?.node;
};

// 修改当前选中组件的props
export const setSelectComponentPropsData = function (
  key: string,
  value: never,
) {
  getSelectComponentNode()?.setPropValue(key, value);
};

// 根据id获取组件实例
export const getNodeById = function (id: string) {
  return project().currentDocument?.getNodeById(id);
};

// 根据组件id进行修改组件的props
export const setSelectComponentPropsDataById = function (
  id: string,
  key: string,
  value: any,
) {
  const node = project().currentDocument?.getNodeById(id);
  if (node) {
    node.setPropValue(key, value);
    node.setPropValue('isPropsChange', true);
  }
};
window.__setSelectComponentPropsDataById = setSelectComponentPropsDataById;
window.__lego_getNodeById = getNodeById;
window.__lego_insertNode = function (...arg) {
  return project().currentDocument?.insertNode(...arg);
};
window.__lego_insertNodes = function (...arg) {
  return project().currentDocument?.insertNodes(...arg);
};

// 获取整体Schema数据
export const getProjectSchema = function () {
  return project().exportSchema();
};

// 获取当前页面报告idURLSearchParams
export const getCurrnetPageId = function () {
  const reportId =
    new URLSearchParams(location.search.slice(1)).get('reportId') ??
    sessionStorage.getItem(LegoReportIdKey);
  return reportId;
};

// 获取Tab 组件当前Slot 组件
export const getCurrentTabSlot = function (node: any) {
  // 获取当前tab 组件active
  const activeKey = store.get(node.id)?.componentTempProps?.tabActiveKey;
  // 查找 slot
  return node.children?.find((v: any) => v?.propsData?.key === activeKey);
};

export const clearLocaPageId = function () {
  sessionStorage[LegoReportIdKey] = '';
};

// 获取当前页面报告名称
export const getCurrnetPageName = function () {
  return config().get('pageName') || '未命名报告';
};

// 当前报告发布状态
export const getCurrnetPageStatus: () => 1 | 0 = function () {
  // // 菜单报告页面直接取1
  if (location.pathname.includes('/qbi/legoBI/report/')) return 1;
  // // 不在qbi 业务的页面取 1
  // if (!location.pathname.includes('/qbi/')) return 1;
  if (location.pathname.includes('/qbi/legoBI/')) {
    return (
      (Number(
        new URLSearchParams(location.search.slice(1)).get('publish'),
      ) as 1) || 0
    );
  }

  return 1;
};

// 获取URL上的城市ID
export const getCurrnetCityId = function () {
  return new URLSearchParams(location.search.slice(1)).get('cityId') ?? '';
};
// 获取URL上的省级ID：getParentDictValue
export const getParentDictValue = function () {
  return new URLSearchParams(location.search.slice(1)).get('pCityId') ?? '';
};

// 获取城市对应的父级id, 注：没有使用递归，值获取第二层数据返回
export const getCityInfo = function (cityTree, id) {
  let pCityId = '';
  let cityInfo = {};
  if (Array.isArray(cityTree)) {
    cityTree?.forEach((item) => {
      if (item.dictValue === id) {
        cityInfo = item;
        pCityId = item.parentDictValue;
      }
    });
  }
  return { cityInfo, pCityId };
};

// 组件遍历器（不含FDRow，FDCell）
export const walkerComponents = function (
  schema: any,
  callback: (
    item: IPublicTypeRootSchema,
    meta: ComponentValue,
    metaMap: { [key: string]: ComponentValue },
  ) => boolean | void,
  componentMeta?: { [key: string]: any },
) {
  const metaMap =
    componentMeta ||
    getComponentMeta()?.reduce((curr: any, next: any) => {
      curr[next.componentName] = next.componentBehavior;
      return curr;
    }, {}) ||
    {};

  const buckets =
    (schema.componentsTree && [...schema.componentsTree]) ||
    (schema.length ? schema : [schema]);

  for (const item of buckets) {
    if (metaMap[item.componentName]) {
      const meta = store.get((item as any).id) || {};
      if (callback(item, meta, metaMap) === false) {
        // break;
        return Promise.reject();
      }
    }
    if (item.children && (item.children as any).length) {
      buckets.push(...(item.children as any));
    }
  }

  return Promise.resolve();
};

// 追溯父级遍历器
export const walkFromParentNode = function (
  node: any,
  callback: (node: any) => any,
): any {
  const res = callback(node);
  if (res !== undefined) return res;
  if (node?.parent) {
    return walkFromParentNode(node.parent, callback);
  }
};

// 遍历树（含FDRow，FDCell）
export const walkTree = (list: any, callback: any) => {
  const walk = (target: any, parent: any, parents: any[]) => {
    target.forEach((item: any, index: number) => {
      callback(item, parent, index, parents);
      if (item?.children?.length) {
        walk(item.children, item, parents.concat([item]));
      }
    });
  };
  walk(list, undefined, []);
};

// 添加品牌组件
export const addBrandComponent = function (schema: any) {
  const filterList = schema.componentsTree[0].children[0].children;
  if (
    !filterList.find(
      (item: any) => item.children[0].componentName === 'BrandFilter',
    )
  ) {
    // 组件不存在，则添加
    filterList.unshift({
      componentName: 'FDCell',
      props: {
        isDefaultFilter: true,
        style: {},
      },
      title: '页面',
      hidden: false,
      isLocked: true,
      condition: true,
      conditionGroup: '',
      children: [
        {
          componentName: 'BrandFilter',
          props: {},
        },
      ],
    });
  }
};

// 删除品牌组件
export const deleteBrandComponent = function (schema: any) {
  const filterList = schema.componentsTree[0].children[0].children;
  const findIndex = filterList.findIndex(
    (item: any) => item.children[0].componentName === 'BrandFilter',
  );
  if (findIndex !== -1) {
    // 组件存在，则删除
    filterList.splice(findIndex, 1);
  }
};

// 将 Schema 数据转为服务所需结构
export const transformSchemaToServer = async function (
  reportName = getCurrnetPageName(),
  schema: any,
) {
  const serverList: any = [];
  const componentMetaMap: { [key: string]: ComponentValue } = {};
  walkerComponents(schema, (item, meta, metaMap) => {
    const bakMeta = (componentMetaMap[item.componentName] =
      metaMap[item.componentName]);
    if (!bakMeta.notQueryData) {
      serverList.push({
        title: item.props?.title || meta.title || bakMeta.title,
        id: item.id,
        componentType: meta.componentType || bakMeta.componentType,
        elementId: meta.elementId || bakMeta.elementId,
        // requestDataType: meta.dataType || bakMeta.dataType,
      });
    }
  });

  const packages = material()?.getAssets()?.packages || [];
  const componentsMap = schema.componentsMap.map((v) => {
    v.componentBehavior = componentMetaMap[v.componentName];
    return v;
  });

  if (isHubble) {
    deleteBrandComponent(schema);
  }

  return {
    reportName: reportName,
    reportId: config().get('reportId'),
    children: serverList,
    schema: JSON.stringify({
      ...schema,
      packages: packages,
      componentsMap,
    }),
  };
};

// 将服务结构转为阿里Schema结构
export const transformServerToSchema = async function (
  server: any,
  customSchema: any,
  tenantId: any,
) {
  const { children, schema, minAuthLevel } = server;

  // 设置在window 上，方便组件内部使用
  window.legoMinAuthLevel = minAuthLevel;

  let _schema = JSON.parse(schema);

  if (customSchema) {
    _schema = await customSchema(_schema, tenantId);
  }

  if (isHubble) {
    addBrandComponent(_schema);
  }

  // 映射 id 与 组件类型
  const chartIdMetaMap: { [key: string]: any } = {};

  // 项目名变更
  _schema.componentsMap.forEach((item: any) => {
    if (item.package === 'leopard-web-react-bi') {
      item.package = 'leopard-web-qbi';
    }
  });
  // 取最新版本
  const assets = getAssets();
  _schema.packages.forEach((item: any) => {
    const find = assets.packages.find((f) => f.library === item.library);
    if (find) {
      item.editUrls = find.editUrls || item.editUrls;
      item.urls = find.urls || item.urls;
      item.package = find.package || item.package;
      item.version = find.version || item.version;
    }
  });
  // 去除 dayjs ， 添加 echarts
  if (!_schema.packages.find((f) => f.library === 'echarts')) {
    _schema.packages.unshift(
      assets.packages.find((f) => f.library === 'echarts'),
    );
  }
  const dayjsIndex = _schema.packages.findIndex((f) => f.library === 'dayjs');
  if (dayjsIndex !== -1) {
    _schema.packages.splice(dayjsIndex, 1);
  }

  const componentsMap = _schema.componentsMap.reduce((curr: any, next: any) => {
    curr[next.componentName] =
      getComponentMetaByName(next.componentName)?.componentBehavior ||
      next.componentBehavior;
    return curr;
  }, {});

  walkerComponents(
    _schema,
    (item, _, metaMap) => {
      if (item.props) {
        item.props.componentId = item.id;
        delete item.props.__regeneration__;
      }
      chartIdMetaMap[item.id as string] = metaMap[item.componentName];
    },
    componentsMap,
  );
  // 做各个旧版本schema的兼容
  // FIX1.0.0: // 1.0.0默认时间控件不可编辑时间，从2.0.0开始就支持了，做向下兼容
  if (_schema.version === '1.0.0') {
    walkTree(_schema.componentsTree, (node: any, parent: any) => {
      if (node.componentName === 'DateFilterGlobal') {
        node.props = { isDefaultFilter: true };
        delete parent.props.isDefaultFilter;
        delete parent.isLocked;
      }
    });
    // 对于1.0.0版本的schema可以直接升级至当前最新版本，已经在FIX1.0.0处做了处理
    _schema.version = curSchemaVersion;
  }

  // 组织模型放量变更

  if (_schema.version < '2.5.0') {
    const newOldMap: any = {
      CityFilter: 'NewCityFilter',
      CapacityCompanyFilter: 'NewCarTeamFilter',
    };
    walkTree(
      _schema.componentsTree,
      (node: any, parent: any, _: number, parents: any[]) => {
        if (node.componentName === 'CapacityCompanyFilter') {
          const index = parents[parents.length - 2].children.findIndex(
            (f: any) => f.children[0].componentName === 'CapacityCompanyFilter',
          );
          parents[parents.length - 2].children.splice(index + 1, 0, {
            componentName: 'FDCell',
            props: {
              isDefaultFilter: true,
              style: {},
            },
            title: '页面',
            hidden: false,
            isLocked: true,
            condition: true,
            children: [
              {
                componentName: 'NewFleetFilter',
                props: {},
              },
            ],
          });
        }
        if (newOldMap[node.componentName]) {
          node.componentName = newOldMap[node.componentName];
        }
      },
    );

    // 调整使用组件的信息
    _schema.componentsMap.forEach((item: any) => {
      if (newOldMap[item.componentName]) {
        item.componentName = newOldMap[item.componentName];
        item.exportName = newOldMap[item.exportName];
      }
    });

    // 添加车队组件信息
    _schema.componentsMap.push({
      componentBehavior: {
        componentType: 4,
        icon: './icon/i.png',
        notQueryData: true,
      },
      componentName: 'NewFleetFilter',
      destructuring: true,
      exportName: 'NewFleetFilter',
      package: 'leopard-web-qbi',
      version: '0.1.0',
      subName: '',
      main: 'src/pages/lego/libraryMaterials/index.tsx',
    });

    // 对于1.0.0版本的schema可以直接升级至当前最新版本，已经在FIX1.0.0处做了处理
    _schema.version = '2.5.0';
  }

  if (_schema.version < '2.6.0') {
    walkTree(
      _schema.componentsTree,
      (node: any, parent: any, _: number, parents: any[]) => {
        if (
          ['DateFilterGlobal', 'DatePickerFilter'].includes(node.componentName)
        ) {
          if (node?.props?.defaultValue?.dateType === 'all') {
            node.props.defaultValue.dateType = 'last-7';
          }
        }
      },
    );
    _schema.version = '2.6.0';
  }

  store.clear();
  children.forEach((item: any) => {
    store.set(item.id, {
      elementId: item.elementId,
      componentType: item.componentType,
      // dataType: item.requestDataType,
      title: item.title,
      ...(chartIdMetaMap[item.id] || {}),
    });
  });

  return _schema;
};

interface defaultValueType {
  category: string;
  dateType: string;
}

// 处理时间控件默认值问题
const getDatePickerDefaultVal = (defaultValue: defaultValueType) => {
  interface keyType {
    [key: string]: object;
  }
  const map: keyType = {
    // 今日
    'cur-day': {
      dateUnit: 'DAY',
      dateInterval: 'CURRENT',
      dateVal: 1,
    },
    // 昨日
    yesterday: {
      dateUnit: 'DAY',
      dateInterval: 'LAST',
      dateVal: 1,
    },
    // 近7天
    'last-7': {
      dateUnit: 'DAY',
      dateInterval: 'CURRENT',
      dateVal: 7,
    },
    // 近15天
    'last-15': {
      dateUnit: 'DAY',
      dateInterval: 'CURRENT',
      dateVal: 15,
    },
    // 本周
    'cur-week': {
      dateUnit: 'WEEK',
      dateInterval: 'CURRENT',
      dateVal: 1,
    },
    // 上周
    'last-week': {
      dateUnit: 'WEEK',
      dateInterval: 'LAST',
      dateVal: 1,
    },
    // 本月
    'cur-month': {
      dateUnit: 'MONTH',
      dateInterval: 'CURRENT',
      dateVal: 1,
    },
    // 上月
    'last-month': {
      dateUnit: 'MONTH',
      dateInterval: 'LAST',
      dateVal: 1,
    },
    // 本季
    'cur-quarter': {
      dateUnit: 'QUARTER',
      dateInterval: 'CURRENT',
      dateVal: 1,
    },
    // 上季
    'last-quarter': {
      dateUnit: 'QUARTER',
      dateInterval: 'LAST',
      dateVal: 1,
    },
  };
  return map[defaultValue?.dateType] || { dateUnit: 'ALL' };
};

// 转换数据集方法
export const transformDataSetConfig = function (
  dataSetConfig: DataSetConfig = {} as DataSetConfig,
  meta: any,
  node?: any,
) {
  const newDataSetConfig: { [key: string]: DatasetItem[] } = {};
  const rest: any = {};
  const sortIndexWeight: any = {
    dataSourceId: 1,
    dimensionInfo: 2,
    measureInfo: 3,
    secondary: 4,
  };
  // 按照dataSourceId、dimensionInfo、measureInfo、secondary次序拼装数据
  Object.keys(dataSetConfig)
    .sort((keya, keyb) => {
      // Object.keys(dataSetConfig)无法保证次序，可能存在先访问secondary后访问measureInfo的情况，导致数据生成错误，所以在这里进行下排序
      return sortIndexWeight[keya] - sortIndexWeight[keyb];
    })
    .forEach((key) => {
      const item: DatasetItem[] = dataSetConfig[key as never];
      if (key === TableSheetHeadSortKey) {
        rest[key] = item;
      }
      if (typeof dataSetConfig[key] !== 'object') {
        // dataSetConfig[key] = item;
        return;
      }
      const config = meta.dataSetConfig?.[key] || ({} as never);
      if (config.type === 'merge') {
        newDataSetConfig[config.to] ||= [];
        newDataSetConfig[config.to].push(
          ...item.map((v) => {
            let mainMeasure;
            if (
              (node.componentName === 'Rank' ||
                node.componentName === 'BarLineChart') &&
              key === 'secondary'
            ) {
              // 对于排行榜、组合图来说，为了兼容旧数据，需要额外兜底处理
              mainMeasure = 0;
            }
            return {
              ...v,
              mainMeasure,
              feConfig: { ...(v.feConfig || {}), source: key },
            };
          }),
        );
      } else if (config.type === 'split') {
        item.forEach((v: DatasetItem, index: number) => {
          newDataSetConfig[v.fieldType] ||= [];
          newDataSetConfig[v.fieldType].push({ ...v, sortNum: index + 1 });
        });
      } else {
        let mainMeasure: any;
        // 如果是日期控件，则需要将默认值放到dimensionInfo[0].defaultVal上
        if (
          (node.componentName === 'Rank' ||
            node.componentName === 'BarLineChart') &&
          key === 'measureInfo'
        ) {
          // 对于排行榜、组合图来说，为了兼容旧数据，需要额外兜底处理
          mainMeasure = 1;
        }
        newDataSetConfig[key] ||= [];
        newDataSetConfig[key].push(...item.map((v) => ({ ...v, mainMeasure })));
        if (
          key === 'dimensionInfo' &&
          node.componentName === 'DatePickerFilter'
        ) {
          // 拖拽上去的时间控件，默认值跟随维度走，前提是设置了维度才会设置defaultVal
          if (newDataSetConfig.dimensionInfo[0]) {
            newDataSetConfig.dimensionInfo[0] = {
              ...newDataSetConfig.dimensionInfo[0],
              defaultVal: getDatePickerDefaultVal(node.propsData?.defaultValue),
            };
          }
        }
      }
    });

  Object.keys(newDataSetConfig).forEach((key) => {
    newDataSetConfig[key].forEach((item: any) => {
      item.feConfig = JSON.stringify(item.feConfig || {});
    });
  });
  if (node && node.componentName === 'DateFilterGlobal') {
    // 默认的时间控件，设置defaultVal
    // @ts-ignore
    newDataSetConfig.dimensionInfo = [
      {
        columnId: 100000010,
        dataType: 2,
        defaultVal: getDatePickerDefaultVal(node.propsData?.defaultValue),
        title: '时间筛选',
      },
    ];
    // @ts-ignore
    newDataSetConfig.datasetId = -1;
  }
  return { ...newDataSetConfig, ...rest };
};

// 注册 resize 监听函数
export const registerResize = function () {
  const resize = function () {
    store.all().forEach(([_, value]) => {
      const list = value?.resize || [];
      list.forEach((fn) => fn());
    });
  };
  resize();
  // window.addEventListener('resize', resize);
  // return function () {
  //   window.removeEventListener('resize', resize);
  // };
};

export function generateRandomNumber() {
  const timestamp = Date.now();
  return timestamp + Math.floor(Math.random() * 100000000);
}

// 当前环境是否为编辑态
export const isDesign = function () {
  return window.location.pathname.includes('/qbi/legoBI/edit');
};

// 获取设计层window 编辑态在iframe中 ， 预览态则在 当前window中
export const getDesignWindow = function () {
  const legoIframe = document.getElementById('lego-iframe-simulatorRenderer');
  return isDesign() ? (legoIframe as HTMLIFrameElement)?.contentWindow : window;
};

// 获取设计层body 元素
export const getDesignBody = function () {
  return getDesignWindow()?.document.getElementById('lego-engine-document');
};

// 命名验证规则
export const isValidName = function (name: string) {
  // const regex = /^[A-Za-z\u4e00-\u9fa5][A-Za-z\u4e00-\u9fa50-9._-]*$/;
  // const noConsecutiveSpecialChars = /[._-]{2,}/; // 检查是否有连续的特殊符号
  // return regex.test(name) && !noConsecutiveSpecialChars.test(name);
  return /^[a-zA-Z0-9_\u4e00-\u9fa5()（）]*$/.test(name);
};
