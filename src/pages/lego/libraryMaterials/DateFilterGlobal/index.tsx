/**
 * 日期组件
 */
import { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import dayjs from 'dayjs';
import { useLegoReport } from '@blm/bi-lego-sdk/dist/es/utils';
import relationCenterExp from '@/pages/lego/libraryMaterials/module/RelationCenter';
import { DatePickerFilter as DatePickerFilterView } from '../../components/Filter/DatePickerFilter';
import { getPresets } from '../../components/Filter/DatePickerFilter/tools';
import useComponent from '../../hooks/useComponent';
import { DEFAULT_DATE_RANGE } from '../DatePickerFilter/constants/dateRangeOptions';
import FilterWrapper from '../wrapper/FilterWrapper';

interface DatePickerFilterProps {
  __id: string;
  componentId: string;
  defaultValue: any;
  disabledType: boolean;
  uuid: string;
  dateRange?: number;
}

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  dataSetConfig: {
    // 用到的组件
    componentName: 'AnalysisConfig',
    // 该组件其他配置项
    props: {
      list: [
        {
          label: '维度',
          key: 'dimensionInfo',
          hideLabelSuffix: true,
          ignoreSetDefaultComputeType: true,
          placeholder: '默认时间维度',
        },
      ],
      indexDisabled: true,
      dimDisabled: true,
      dataSetDisabled: true,
    },
  },
  // 默认时间
  defaultValue: {
    componentName: 'DatePickerDefaultValue',
    // 该组件其他配置项
    props: {},
  },

  // 是否禁用时间组件
  disabledType: {
    componentName: 'DatePickerDisabled',
    // 该组件其他配置项
    props: {},
  },

  // 是否隐藏
  hidden: {
    componentName: 'HiddenSetter',
    // 该组件其他配置项
    props: {
      noPadding: true,
    },
  },
  // 跨度可选范围
  dateRange: {
    componentName: 'DatePickerRange',
    // 该组件其他配置项
    props: {},
  },
  // 别名
  title: {
    componentName: 'AliasSetter',
    props: {
      isDate: true,
    },
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图`表 4:筛选器 5:文本 41:默认筛选器
  componentType: 410,
  // 不参与获取数据集相关事情
  notQueryData: true,
};

const temp_dateMap: any = {};
setTimeout(() => {
  const { project } = window.AliLowCodeEngine || {};
  // 暂时通过监听历史变化，重置缓存，后面统一处理默认值问题
  project?.currentDocument?.history.onChangeCursor(() => {
    // eslint-disable-next-line guard-for-in
    for (let key in temp_dateMap) {
      delete temp_dateMap[key];
    }
  });
});

// 逻辑层
export const DateFilterGlobal = (props: DatePickerFilterProps) => {
  const {
    __id,
    componentId,
    defaultValue,
    disabledType,
    dateRange = DEFAULT_DATE_RANGE,
    uuid,
  } = props;
  const relationCenter = relationCenterExp(uuid);
  const defaultValueRef = useRef(defaultValue);
  const dateRangeRef = useRef(dateRange);
  const filterId = __id ?? componentId ?? '';
  const presets = getPresets();
  const [_, setMeta] = useComponent(filterId);
  const [reportMeta] = useLegoReport(uuid);
  // 时间单位
  const dateUnitRef = useRef(6);
  const isEdit = props.__designMode === 'design';

  // 重置强制刷新用
  const [key, setKey] = useState(() => Date.now());

  const getDefaultDateItem = () => {
    const dateType = defaultValue?.dateType;
    // 外部自定义默认时间
    if (dateType === 'customDate') {
      // 如果 defaultValue 中有自定义的 value，使用它；否则使用默认值
      const customValue =
        defaultValue?.value || presets.find((item) => item.type === 'last-7');
      return {
        label: null,
        title: '自定义',
        timeType: 6,
        type: 'customDate',
        value: customValue,
      };
    }
    // 快捷默认时间
    return (
      presets.find((item) => item.type === dateType) ||
      presets.find((item) => item.type === 'last-7')
    );
  };

  const defaultDateItem = getDefaultDateItem();
  // 配置项变更手动重置为当前默认值，并刷新图表数据
  const handleDateChange = useCallback(
    (val, newField) => {
      if (dateRangeRef.current !== dateRange) {
        dateRangeRef.current = dateRange;
        temp_dateMap[filterId] = {
          value: defaultDateItem?.value,
          type: temp_dateMap[filterId]?.type,
        };
        setKey(Date.now());
      }
      if (defaultValue?.dateType === 'customDate') {
        defaultValueRef.current = defaultValue;
        temp_dateMap[filterId] = {
          value:
            defaultValue?.value ||
            presets.find((item) => item.type === 'last-7'),
          type: 6, // CUSTOM 类型
        };
        dateUnitRef.current = 6;
      } else if (
        JSON.stringify(defaultValueRef.current) !== JSON.stringify(defaultValue)
      ) {
        defaultValueRef.current = defaultValue;
        dateUnitRef.current = defaultDateItem.timeType;
        temp_dateMap[filterId] = null;
        setKey(Date.now());
      }
    },
    [dateRange, defaultValue, filterId, presets],
  );

  const handleFieldLabel = (value: any[]) => {
    return value?.length
      ? [
          dayjs(value[0]).startOf('day').format('YYYY-MM-DD') +
            ' ~ ' +
            dayjs(value[1]).endOf('day').format('YYYY-MM-DD'),
        ]
      : [];
  };

  useEffect(() => {
    setMeta(
      {
        query() {
          relationCenter.notify('all');
        },
      },
      true,
    );
  }, []);

  //支持动态渲染
  return (
    <FilterWrapper
      componentProps={props}
      label={
        isEdit || reportMeta.oldVersion > '2.6.0'
          ? props.title || '筛选日期'
          : false
      }
      fieldProps={useMemo(() => {
        return {
          columnId: 100000010,
          key: 't_date',
          dataType: 2,
        };
      }, [])}
      defaultValue={[
        dayjs(defaultDateItem?.value[0])
          .startOf('day')
          .valueOf(),
        dayjs(defaultDateItem?.value[1])
          .endOf('day')
          .valueOf(),
      ]}
      onChange={handleDateChange}
      handleFieldLabel={handleFieldLabel}
      filterExtendField={(field, resetType) => {
        return {
          dateFilterRelativeUnit: resetType
            ? defaultDateItem.timeType
            : dateUnitRef.current,
        };
      }}
    >
      {({ value, onChange, fieldItem }) => {
        let valueChange = [] as any;
        if (value) {
          valueChange = [dayjs(value[0]).toDate(), dayjs(value[1]).toDate()];
        }
        return (
          <DatePickerFilterView
            key={key}
            handleChange={(date, dateUnit) => {
              dateUnitRef.current = dateUnit;
              onChange(date, dateUnit);
            }}
            defaultValue={defaultDateItem}
            disabledType={disabledType}
            maxStep={dateRange - 1}
            value={valueChange}
            placeholder={
              reportMeta.oldVersion > '2.6.0' && (props.title || '日期')
            }
          />
        );
      }}
    </FilterWrapper>
  );
};

DateFilterGlobal.displayName = '默认时间筛选器';
