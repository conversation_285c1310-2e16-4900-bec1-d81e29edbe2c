import { Table as TableView } from '@/pages/lego/components';
import type { ComponentProps } from '../../type';
import ComponentWrapper from '../../components/module/ComponentWrapper';
import isMobile from '../../utils/isMobile';
import { handleData } from './handleData';

export const LinkSetterComponent = {
  dataSetConfig: {
    // 用到的组件
    componentName: 'AnalysisConfig',
    // 该组件其他配置项
    props: {
      list: [
        {
          label: '列',
          key: 'dimensionInfo',
          type: 'split',
          limit: [1, Infinity],
          numberFormatConfig: true,
          // 只格式化指标类字段
          numberFormatIndexOnly: true,
          // 不加默认聚合方式
          ignoreSetDefaultComputeType: true,
          combineModeIndex: {
            total: true,
          },
          // 是否配置字段名称
          fieldAliasConfig: true,
        },
      ],
    },
  },
  operationCols: {
    componentName: 'OperationColumnConfig',
    props: {},
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 2,
  // 该组件用到的数据类型
  dataType: 41,
};

export const Table = (props: ComponentProps<any>) => {
  const dimensionInfo = props?.dataSetConfig?.dimensionInfo ?? {};
  return ComponentWrapper(TableView, {
    // 增加数据处理层
    handleData: handleData(dimensionInfo),
    // 标题配置
    titleConfig: {
      show: true,
      border: false,
    },
    notDefaultStyle: true,
    defaultHeight: isMobile() ? 'auto' : undefined,
    wrapperStyle: {
      width: '100%',
      height: isMobile() ? undefined : 'calc(100% - 24px)',
      overflow: 'auto',
      marginTop: isMobile() ? 0 : '-12px',
    },
    wrapperClass: 'lego-table-container',
  })(props);
};
Table.displayName = '明细表';
