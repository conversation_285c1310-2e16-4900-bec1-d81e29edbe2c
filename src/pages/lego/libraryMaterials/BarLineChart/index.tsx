import { Coordinate } from '@blmcp/charts';
import { ComponentProps } from '@/pages/lego/type';
import {
  formatValue,
  transformDataSet,
} from '@/pages/lego/libraryMaterials/module/utils';
import ComponentWrapper from '../../components/module/ComponentWrapper';

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  dataSetConfig: {
    // 用到的组件
    componentName: 'AnalysisConfig',
    // 该组件其他配置项
    props: {
      list: [
        {
          label: 'X轴',
          key: 'dimensionInfo',
          // 维度聚合方式
          combineModeDim: {},
          // 指标聚合方式
          combineModeIndex: {},
          onlyOne: true,
          placeholder: '仅支持拖入 1 个字段',
          limit: [1, 1],
          ignoreSetDefaultComputeType: true,
        },
        {
          label: 'Y轴',
          key: 'measureInfo',
          rateDependKey: 'dimensionInfo',
          dateFormatDisabled: true,
          numberFormatConfig: true,
          // 维度聚合方式
          combineModeDim: {
            aggregate: [
              {
                key: 1,
                label: '计数',
              },
              {
                key: 2,
                label: '去重计数',
              },
            ],
          },
          combineModeIndex: {
            aggregate: [
              {
                key: 6,
                label: '求和',
              },
              {
                key: 1,
                label: '计数',
              },
              {
                key: 5,
                label: '平均值',
              },
              {
                key: 2,
                label: '去重计数',
              },
              {
                key: 3,
                label: '最大值',
              },
              {
                key: 4,
                label: '最小值',
              },
            ],
            quick: [
              {
                key: 'rate',
                label: '同环比',
              },
              {
                key: 'percent',
                label: '百分比',
                column: true,
              },
            ],
          },
          onlyOne: false,
          // placeholder: '仅支持拖入 1 个字段',
          // 指标聚合方式
          limit: [1, Infinity],
          // 是否配置字段名称
          fieldAliasConfig: true,
        },
        {
          label: '次轴',
          key: 'secondary',
          type: 'merge',
          to: 'measureInfo',
          rateDependKey: 'dimensionInfo',
          dateFormatDisabled: true,
          numberFormatConfig: true,
          combineModeDim: {
            aggregate: [
              {
                key: 1,
                label: '计数',
              },
              {
                key: 2,
                label: '去重计数',
              },
            ],
          },
          combineModeIndex: {
            aggregate: [
              {
                key: 6,
                label: '求和',
              },
              {
                key: 1,
                label: '计数',
              },
              {
                key: 5,
                label: '平均值',
              },
              {
                key: 2,
                label: '去重计数',
              },
              {
                key: 3,
                label: '最大值',
              },
              {
                key: 4,
                label: '最小值',
              },
            ],
            quick: [
              {
                key: 'rate',
                label: '同环比',
              },
              {
                key: 'percent',
                label: '百分比',
                column: true,
              },
            ],
          },
          onlyOne: false,
          // placeholder: '仅支持拖入 1 个字段',
          limit: [1, Infinity],
          // 是否配置字段名称
          fieldAliasConfig: true,
        },
      ],
    },
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 3,
  // 该组件用到的数据类型
  dataType: 9,
  // 组件最多一行几个
  maxRows: 3,
  // 是否可以关联等等
};

export const BarLineChart = function (props: ComponentProps<any>) {
  return ComponentWrapper(Coordinate, {
    handleData(dataSource) {
      return transformDataSet(dataSource, (type, item, index) => {
        if (type === 'measureInfo') {
          try {
            const config = JSON.parse(item?.feConfig || '{}');
            return {
              seriesType: config.source === 'secondary' ? 'line' : 'bar',
              yAxisIndex: config.source === 'secondary' ? 1 : undefined,
            };
          } catch {
            return {
              seriesType: index === 1 ? 'line' : 'bar',
              yAxisIndex: index === 0 ? undefined : 1,
            };
          }
        }
      });
    },
    handleProps(props, dataSource) {
      return {
        ...props,
        tooltipFormatter: formatValue,
        yAxis0Formatter: (value: number, dimensions: any[]) => {
          if (
            dimensions.every(
              (e) =>
                e.advanceComputeModeId === 30 ||
                e.valueFormat === 2 ||
                e.numberFormat === 2,
            )
          ) {
            return Number((value * 100).toFixed(2)) + '%';
          }
          return Number(value.toFixed(2));
        },
        yAxis1Formatter: (value: number, dimensions: any[]) => {
          if (
            dimensions.every(
              (e) =>
                e.advanceComputeModeId === 30 ||
                e.valueFormat === 2 ||
                e.numberFormat === 2,
            )
          ) {
            return Number((value * 100).toFixed(2)) + '%';
          }
          return Number(value.toFixed(2));
        },
        label: dataSource?.values?.length === 1,
        handleOption(option: any) {
          option.series.forEach((v: any) => {
            if (v.type === 'bar') {
              v.label.show = false;
            }
          });
          return option;
        },
      };
    },
  })(props);
};

BarLineChart.displayName = '组合图';
