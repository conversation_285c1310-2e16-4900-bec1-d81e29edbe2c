/**
 * 交叉表格，用于在表格中分组展示数据，
 */

import { TableSheet as TableSheetView } from '@/pages/lego/components';
import { ComponentProps } from '../../type';
import ComponentWrapper from '../../components/module/ComponentWrapper';
import isMobile from '../../utils/isMobile';
import { handleData } from './handleData';
import './index.less';

export const LinkSetterComponent = {
  dataSetConfig: {
    // 用到的组件
    componentName: 'AnalysisConfig',
    // 该组件其他配置项

    props: {
      list: [
        {
          label: '维度',
          key: 'dimensionInfo',
          indexDisabled: true,
          limit: [1, Infinity],
          ignoreSetDefaultComputeType: true, // 忽略默认设置聚合方式
          // 设置字段别名
          fieldAliasConfig: true,
        },
        {
          label: '对比',
          key: 'contrastInfo',
          indexDisabled: true,
          limit: [0, Infinity],
          ignoreSetDefaultComputeType: true,
          disabledSortConfig: true,
        },
        {
          label: '数值',
          key: 'measureInfo',
          limit: [1, Infinity],
          //TODO:二期开启
          //rateDependKey: ['dimensionInfo', 'contrastInfo'],
          rateDependKey: ['dimensionInfo'],
          // 排序设置隐藏所依赖的字段
          sortHideDepends: 'contrastInfo',
          numberFormatConfig: true,
          dateFormatDisabled: true,

          // 指标聚合方式
          combineModeIndex: {
            aggregate: [
              {
                key: 6,
                label: '求和',
              },
              {
                key: 1,
                label: '计数',
              },
              {
                key: 5,
                label: '平均值',
              },
              {
                key: 2,
                label: '去重计数',
              },
              {
                key: 3,
                label: '最大值',
              },
              {
                key: 4,
                label: '最小值',
              },
            ],
            quick: [
              {
                key: 'rate',
                label: '同环比',
              },
              {
                key: 'percent',
                label: '百分比',
                column: true,
              },
            ],
            total: true,
          },
          combineModeDim: {
            aggregate: [
              {
                key: 1,
                label: '计数',
              },
              {
                key: 2,
                label: '去重计数',
              },
            ],
          },
          // 设置字段别名
          fieldAliasConfig: true,
        },
      ],
      //TODO:二期开启
      // headSortFlag: true
    },
  },
  operationCols: {
    componentName: 'OperationColumnConfig',
    props: {},
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 2,
  // 该组件用到的数据类型
  dataType: 5,
};

export const TableSheet = (props: ComponentProps<any>) => {
  return ComponentWrapper(TableSheetView, {
    // 增加数据处理层
    handleData: handleData,
    // 标题配置
    titleConfig: {
      show: true,
      border: false,
    },
    notDefaultStyle: true,
    defaultHeight: isMobile() ? 'auto' : undefined,
    wrapperStyle: {
      width: '100%',
      height: isMobile() ? undefined : 'calc(100% - 20px)',
      overflow: 'auto',
      marginTop: isMobile() ? 0 : '-12px',
    },
    wrapperClass: 'lego-table-container',
  })(props);
};
TableSheet.displayName = '交叉表';
