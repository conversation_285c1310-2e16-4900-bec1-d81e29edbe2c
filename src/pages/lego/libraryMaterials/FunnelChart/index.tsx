import { FunnelChart as FunnelChartView } from '@/pages/lego/components';
import { ComponentProps } from '@/pages/lego/type';
import ComponentWrapper from '../../components/module/ComponentWrapper';
import { transformData } from './handleData';

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  dataSetConfig: {
    // 用到的组件
    componentName: 'AnalysisConfig',
    // 该组件其他配置项
    props: {
      list: [
        {
          label: '维度',
          key: 'dimensionInfo',
          limit: [0, 1],
          onlyOne: true,
          placeholder: '仅支持拖入 1 个字段',
          combineModeIndex: {},
          combineModeDim: {},
          ignoreSetDefaultComputeType: true,
          disabledDrop: 'measureInfo',
        },
        {
          label: '数值',
          key: 'measureInfo',
          rateDependKey: 'dimensionInfo',
          // 隐藏同环比中，增长率
          hideRate: true,
          dateFormatDisabled: true,
          numberFormatConfig: true,
          onlyOneLimit: 'dimensionInfo',
          // 指标聚合方式
          combineModeIndex: {
            aggregate: [
              {
                key: 6,
                label: '求和',
              },
              {
                key: 1,
                label: '计数',
              },
              {
                key: 5,
                label: '平均值',
              },
              {
                key: 2,
                label: '去重计数',
              },
              {
                key: 3,
                label: '最大值',
              },
              {
                key: 4,
                label: '最小值',
              },
            ],
            quick: [
              {
                key: 'rate',
                label: '同环比',
              },
              // {
              //   key: 'percent',
              //   label: '百分比',
              //   column: true,
              // },
            ],
          },
          // 维度聚合方式
          combineModeDim: {
            aggregate: [
              {
                key: 1,
                label: '计数',
              },
              {
                key: 2,
                label: '去重计数',
              },
            ],
          },
          placeholder: '请将字段拖至此处',
          limit: [1, Infinity],
          // 是否配置字段名称
          fieldAliasConfig: true,
        },
      ],
    },
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 3,
  // 该组件用到的数据类型
  dataType: 10, //10,
};
export const FunnelChart = function (props: ComponentProps<any>) {
  return ComponentWrapper(FunnelChartView, {
    // 增加数据处理层
    handleData: transformData,
  })(props);
};

FunnelChart.displayName = '漏斗图';
