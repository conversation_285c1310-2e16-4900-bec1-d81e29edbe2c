import { ComponentProps } from '@/pages/lego/type';
import ComponentWrapper from '../../components/module/ComponentWrapper';
import { TextComponent } from '../../components/Text';
import isMobile from '../../utils/isMobile';
import { useLegoReport } from '@blm/bi-lego-sdk/dist/es/utils';

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 5,
};

export const LinkSetterComponent = {
  dataSetConfig: {
    // 用到的组件
    componentName: 'AnalysisConfig',
    // 该组件其他配置项
    props: {
      desc: '请在左侧布局区域输入文字内容',
      list: [],
      dimDisabled: true,
      indexDisabled: true,
      saveSwitchDataSourceId: true,
      addDataSetDisabled: true,
    },
  },
  desc: {
    componentName: 'DescriptionConfig',
    props: {
      desc: '请在左侧布局区域输入文字内容',
    },
  },
};

export const Text = function (props: ComponentProps<any>) {
  const { uuid } = props;
  const [reportMeta] = useLegoReport(uuid);
  const defaultHeight = () => {
    if (isMobile()) {
      return 'auto';
    } else {
      if (props.__designMode === 'design') {
        return 200;
      } else {
        if (reportMeta.oldVersion > '2.6.0') {
          return 'auto';
        } else {
          return 126;
        }
      }
    }
  };
  return ComponentWrapper(TextComponent, {
    notQueryData: true,
    notDefaultStyle: false,
    defaultHeight: defaultHeight(),
    showTitleArrow: isMobile(),
  })(props);
};

Text.displayName = '富文本';
